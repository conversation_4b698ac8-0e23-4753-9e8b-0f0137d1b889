"""
Pipeline wrapper for the HFO detection algorithm.

This module provides a clean interface to the original HFO detection algorithm
while preserving its exact functionality. No algorithmic changes are made here.
"""

from typing import Dict, Any, Optional, Callable
import logging
import os
from .hfo_analysis import run_hfo_algorithm


class HFODetectionPipeline:
    """
    Wrapper class for the HFO detection algorithm.

    This class provides a cleaner interface to the original algorithm
    without changing any of the core detection logic.
    """

    def __init__(self, use_refactored: bool = False):
        """
        Initialize the pipeline wrapper

        Args:
            use_refactored: If True, use the refactored modular approach
        """
        self.logger = logging.getLogger(__name__)
        self._last_result = None
        # Force use of the legacy implementation to remain in lockstep with the
        # reference algorithm. The use_refactored flag and env var are ignored
        # intentionally so only the canonical path executes.
        self.use_refactored = False

    def run(
        self,
        eeg_data: Dict[str, Any],
        file_path: str,
        parameters: Dict[str, Any],
        gui_output: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Run the HFO detection algorithm.

        This is a wrapper around the original run_hfo_algorithm function
        that preserves its exact behavior while providing a cleaner interface.

        Args:
            eeg_data: EEG data dictionary with keys:
                - 'data': numpy array of EEG data
                - 'nbchan': number of channels
                - 'srate': sampling rate
                - 'chanlocs': channel locations
                - etc.
            file_path: Path to the EDF file
            parameters: Detection parameters dictionary with keys:
                - 'analysis_start': Start time in seconds
                - 'analysis_end': End time in seconds
                - 'montage': Montage type
                - 'user_ref': User reference (for referential montage)
                - 'low_cutoff': Low frequency cutoff
                - 'high_cutoff': High frequency cutoff
                - 'amplitude_1': Threshold option 1
                - 'amplitude_2': Threshold option 2
                - 'peaks_1': Threshold option 3
                - 'peaks_2': Threshold option 4
                - 'duration': Threshold option 5
                - 'temporal_sync': Threshold option 6
                - 'spatial_sync': Threshold option 7
            gui_output: Optional callback for GUI output

        Returns:
            Dict with detection results
        """
        # Log the analysis start
        self.logger.info(f"Starting HFO detection for {file_path}")
        self.logger.info("Using original reference-aligned implementation")

        # Clamp cutoffs to valid range (0 < Wn < 1)
        sampling_rate = float(eeg_data.get('srate', 500))
        nyquist = max(1.0, sampling_rate / 2.0)
        requested_low = float(parameters.get('low_cutoff', 50))
        requested_high = float(parameters.get('high_cutoff', 300))

        min_low = 0.5
        clamped_low = max(min_low, min(requested_low, nyquist - 1.0))
        clamped_high = max(clamped_low + 0.5,
                           min(requested_high, nyquist - 1.0))

        if clamped_low != requested_low or clamped_high != requested_high:
            self.logger.warning(
                f"Adjusted cutoffs from ({requested_low}, {requested_high})Hz to "
                f"({clamped_low}, {clamped_high})Hz for Nyquist={nyquist}Hz"
            )
            parameters = {**parameters, 'low_cutoff': clamped_low,
                          'high_cutoff': clamped_high}

        # Only run the legacy implementation to stay aligned with the reference code
        result = run_hfo_algorithm(
            EEG=eeg_data,
            input_file_path=file_path,
            analysis_start=parameters.get('analysis_start', 0),
            analysis_end=parameters.get('analysis_end', -1),
            montage=parameters.get('montage', 'Bipolar'),
            user_ref=parameters.get('user_ref', None),
            locutoff=parameters.get('low_cutoff', 50),
            hicutoff=parameters.get('high_cutoff', 300),
            gui_output=gui_output,
            threshold_option1=parameters.get('amplitude_1', 2),
            threshold_option2=parameters.get('amplitude_2', 2),
            threshold_option3=parameters.get('peaks_1', 6),
            threshold_option4=parameters.get('peaks_2', 3),
            threshold_option5=parameters.get('duration', 10),
            threshold_option6=parameters.get('temporal_sync', 10),
            threshold_option7=parameters.get('spatial_sync', 10)
        )

        # Store the result for potential debugging
        self._last_result = result

        # Log the completion
        if result.get('success'):
            self.logger.info("HFO detection completed successfully")
        else:
            self.logger.error("HFO detection failed")

        return result

    def prepare_parameters(self, user_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert user-friendly parameter names to algorithm parameter names.

        This helps bridge between modern parameter naming and the legacy
        algorithm parameter names.

        Args:
            user_params: User-provided parameters with friendly names

        Returns:
            Dict with algorithm-compatible parameter names
        """
        # Map user-friendly names to algorithm names
        param_mapping = {
            'start_time': 'analysis_start',
            'end_time': 'analysis_end',
            'montage_type': 'montage',
            'reference_channel': 'user_ref',
            'low_frequency': 'low_cutoff',
            'high_frequency': 'high_cutoff',
            'amplitude_threshold_1': 'amplitude_1',
            'amplitude_threshold_2': 'amplitude_2',
            'min_peaks_1': 'peaks_1',
            'min_peaks_2': 'peaks_2',
            'min_duration_ms': 'duration',
            'temporal_sync_ms': 'temporal_sync',
            'spatial_sync_ms': 'spatial_sync'
        }

        # Convert parameters
        algorithm_params = {}
        for user_key, algo_key in param_mapping.items():
            if user_key in user_params:
                algorithm_params[algo_key] = user_params[user_key]

        # Add any parameters that don't need mapping
        for key, value in user_params.items():
            if key not in param_mapping and key not in algorithm_params:
                algorithm_params[key] = value

        return algorithm_params

    def validate_parameters(self, parameters: Dict[str, Any]) -> bool:
        """
        Validate parameters before running the algorithm.

        Args:
            parameters: Parameters to validate

        Returns:
            bool: True if parameters are valid
        """
        # Check required parameters
        required = ['analysis_start', 'analysis_end', 'montage',
                    'low_cutoff', 'high_cutoff']

        for param in required:
            if param not in parameters:
                self.logger.error(f"Missing required parameter: {param}")
                return False

        # Validate frequency range
        if parameters['low_cutoff'] >= parameters['high_cutoff']:
            self.logger.error("Low cutoff must be less than high cutoff")
            return False

        # Validate time range
        if parameters['analysis_end'] != -1:
            if parameters['analysis_start'] >= parameters['analysis_end']:
                self.logger.error("Start time must be less than end time")
                return False

        # Validate montage type
        valid_montages = ['Bipolar', 'Average', 'Referential']
        if parameters['montage'] not in valid_montages:
            self.logger.error(f"Invalid montage: {parameters['montage']}")
            return False

        return True

    def get_last_result(self) -> Optional[Dict[str, Any]]:
        """
        Get the last analysis result.

        Returns:
            Dict with last result or None if no analysis has been run
        """
        return self._last_result

    def format_result_summary(self, result: Dict[str, Any]) -> str:
        """
        Format a human-readable summary of the detection results.

        Args:
            result: Result dictionary from the algorithm

        Returns:
            str: Formatted summary
        """
        if not result or not result.get('success'):
            return "Analysis failed or no results available"

        summary_lines = [
            "HFO Detection Summary",
            "=" * 50
        ]

        # Add basic statistics if available
        if 'total_hfos' in result:
            summary_lines.append(
                f"Total HFOs detected: {result['total_hfos']}")

        if 'channels_analyzed' in result:
            summary_lines.append(
                f"Channels analyzed: {result['channels_analyzed']}")

        if 'duration_analyzed' in result:
            summary_lines.append(
                f"Duration analyzed: {result['duration_analyzed']:.2f} seconds")

        if 'hfo_rate' in result:
            summary_lines.append(
                f"HFO rate: {result['hfo_rate']:.2f} per minute")

        return "\n".join(summary_lines)


class PipelineProgressReporter:
    """
    Helper class for reporting pipeline progress.

    This can be used to provide feedback during long-running analyses.
    """

    def __init__(self, callback: Optional[Callable] = None):
        """
        Initialize the progress reporter.

        Args:
            callback: Optional callback function for progress updates
        """
        self.callback = callback
        self.current_step = ""
        self.total_steps = 0
        self.completed_steps = 0

    def start(self, total_steps: int):
        """Start progress tracking"""
        self.total_steps = total_steps
        self.completed_steps = 0
        self._report("Analysis started")

    def update(self, step_name: str):
        """Update current step"""
        self.current_step = step_name
        self.completed_steps += 1
        progress = (self.completed_steps / self.total_steps) * \
            100 if self.total_steps > 0 else 0
        self._report(f"{step_name} ({progress:.1f}%)")

    def complete(self):
        """Mark analysis as complete"""
        self._report("Analysis complete")

    def _report(self, message: str):
        """Report progress"""
        if self.callback:
            self.callback(message)

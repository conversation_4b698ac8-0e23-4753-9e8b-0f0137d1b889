import React, { useState } from "react";
import { ChannelGrid, type ZoomLevel } from "./ChannelGrid";

// Generate mock EEG data for testing
const generateMockData = (channels: number, samples: number): Record<string, number[]> => {
  const data: Record<string, number[]> = {};
  
  for (let i = 1; i <= channels; i++) {
    const channelName = `EEG ${i.toString().padStart(2, '0')}`;
    const channelData: number[] = [];
    
    for (let j = 0; j < samples; j++) {
      // Generate realistic EEG-like signal with some noise and oscillations
      const time = j / 1000; // Assuming 1000 Hz sampling rate
      const baseSignal = Math.sin(2 * Math.PI * 10 * time) * 50; // 10 Hz base frequency
      const noise = (Math.random() - 0.5) * 20;
      const drift = Math.sin(2 * Math.PI * 0.5 * time) * 30; // Slow drift
      
      channelData.push(baseSignal + noise + drift);
    }
    
    data[channelName] = channelData;
  }
  
  return data;
};

export const ZoomTestComponent: React.FC = () => {
  const [zoomLevel, setZoomLevel] = useState<ZoomLevel>('fit-all');
  
  // Generate mock data for testing - 50 channels, 10 seconds at 1000 Hz
  const mockChannelData = generateMockData(50, 10000);
  const mockChannels = Object.keys(mockChannelData);
  
  return (
    <div className="w-full h-screen bg-gray-100 p-4">
      <div className="bg-white rounded-lg shadow-lg h-full flex flex-col">
        {/* Header with zoom controls */}
        <div className="flex-shrink-0 h-16 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-900">EEG Zoom Test</h2>
            <span className="text-sm text-gray-600 bg-white px-3 py-1 rounded">
              {mockChannels.length} channels
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 mr-2">Zoom Level:</span>
            <select
              value={zoomLevel}
              onChange={(e) => setZoomLevel(e.target.value as ZoomLevel)}
              className="text-sm border border-gray-300 rounded px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="fit-all">Fit All (Overview)</option>
              <option value="small">Small (30px)</option>
              <option value="medium">Medium (50px)</option>
              <option value="large">Large (80px)</option>
              <option value="extra-large">Extra Large (120px)</option>
            </select>
          </div>
        </div>
        
        {/* Channel Grid */}
        <div className="flex-1 overflow-hidden">
          <ChannelGrid
            channelData={mockChannelData}
            visibleChannels={mockChannels}
            timeWindow={[0, 10]} // Show 10 seconds
            samplingRate={1000}
            hfoEvents={[]}
            showHFOMarkers={false}
            zoomLevel={zoomLevel}
            gain={20}
          />
        </div>
        
        {/* Info panel */}
        <div className="flex-shrink-0 h-12 bg-gray-50 border-t border-gray-200 px-4 flex items-center">
          <div className="text-xs text-gray-600">
            Current zoom: <strong>{zoomLevel}</strong> | 
            Channels: <strong>{mockChannels.length}</strong> | 
            Duration: <strong>10 seconds</strong>
          </div>
        </div>
      </div>
    </div>
  );
};

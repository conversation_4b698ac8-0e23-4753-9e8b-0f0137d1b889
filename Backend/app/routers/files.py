"""
File operations router
"""

from fastapi import APIRouter

from ..constants import DEFAULT_EXPIRY_SECONDS
from ..models import (
    ErrorResponse,
    FileDeleteRequest,
    FileDeleteResponse,
    FileListResponse,
)
from ..services.s3_service import s3_service
from ..utils.error_handlers import handle_not_found, handle_s3_error
from ..utils.file_utils import extract_filename_from_key

router = APIRouter(prefix="/files", tags=["files"])


# File listing
@router.get(
    "/list",
    response_model=FileListResponse,
    responses={500: {"model": ErrorResponse, "description": "Server error"}},
)
async def list_files(prefix: str | None = None):
    """List all files in S3 bucket"""
    try:
        files = s3_service.list_files(prefix)
        total_size = sum(file.size for file in files)

        return FileListResponse(
            files=files, total_count=len(files), total_size=total_size
        )
    except Exception as e:
        handle_s3_error("list files", e)


# File deletion
@router.delete(
    "/delete",
    response_model=FileDeleteResponse,
    responses={
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"},
    },
)
async def delete_file(request: FileDeleteRequest):
    """Delete file from S3 bucket"""
    try:
        metadata = s3_service.get_file_metadata(request.key)
        if not metadata:
            handle_not_found("File", request.key)

        success = s3_service.delete_file(request.key)

        return FileDeleteResponse(
            success=success,
            message="File deleted successfully",
            deleted_key=request.key,
        )
    except Exception as e:
        if hasattr(e, "status_code"):
            raise
        handle_s3_error("delete file", e, request.key)


# File metadata (EDF info)
@router.get(
    "/metadata/{key:path}",
    responses={
        200: {"description": "File metadata including EDF channels"},
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"},
    },
)
async def get_file_metadata(key: str):
    """Get file metadata including EDF channels and sampling rate"""
    try:
        from ..utils.edf_binary_reader import get_edf_info_from_bytes

        # First, read just the main header (256 bytes) to determine number of signals
        initial_header = s3_service.get_file_range(key, 0, 255)
        if not initial_header:
            handle_not_found("File", key)

        # Parse the number of signals from the header
        # Position 252-255 contains the number of signals (4 bytes)
        num_signals_str = (
            initial_header[252:256].decode("ascii", errors="replace").strip()
        )
        num_signals = int(num_signals_str) if num_signals_str.isdigit() else 0

        # Calculate total header size
        # EDF header = 256 bytes (main) + (256 bytes × number of signals)
        total_header_size = 256 + (256 * num_signals)

        # Read the complete header with a reasonable maximum limit
        # Cap at 51200 bytes (~200 signals) to prevent excessive reads
        header_size_to_read = min(total_header_size, 51200)

        # Get the full header data
        header_data = s3_service.get_file_range(key, 0, header_size_to_read - 1)
        if not header_data:
            handle_not_found("File", key)

        # Extract metadata from bytes
        edf_info = get_edf_info_from_bytes(header_data)

        # Add filename to the result
        edf_info["filename"] = extract_filename_from_key(key)

        # If no channels were extracted, there might be an issue with the file
        if not edf_info.get("channels"):
            # Log the error if present
            if edf_info.get("error"):
                print(f"Error reading EDF file {key}: {edf_info['error']}")

        return edf_info

    except Exception as e:
        # Try fallback method with full file download if partial read fails
        try:
            import os
            import tempfile

            from ..utils.edf_binary_reader import get_edf_info

            print(f"Partial read failed for {key}, falling back to full download: {e}")

            # Get full file from S3
            file_data = s3_service.get_file(key)
            if not file_data:
                handle_not_found("File", key)

            # Save to temp file and read EDF metadata
            with tempfile.NamedTemporaryFile(suffix=".edf", delete=False) as tmp_file:
                tmp_file.write(file_data)
                tmp_path = tmp_file.name

            try:
                # Use our custom binary EDF reader that handles all EDF types
                edf_info = get_edf_info(tmp_path)

                # Add filename to the result
                edf_info["filename"] = extract_filename_from_key(key)

                return edf_info

            finally:
                # Clean up temp file
                if os.path.exists(tmp_path):
                    os.remove(tmp_path)

        except Exception as fallback_error:
            # Return error metadata
            return {
                "channels": [],
                "sampling_rate": 256,
                "duration_seconds": 0,
                "start_datetime": None,
                "num_channels": 0,
                "filename": extract_filename_from_key(key),
                "error": str(fallback_error),
            }


# File download
@router.get(
    "/download/{key:path}",
    responses={
        200: {"description": "Download URL"},
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"},
    },
)
async def get_download_url(key: str):
    """Generate download URL for file"""
    try:
        metadata = s3_service.get_file_metadata(key)
        if not metadata:
            handle_not_found("File", key)

        url = s3_service.generate_download_url(key)

        return {
            "download_url": url,
            "expires_in": DEFAULT_EXPIRY_SECONDS,
            "filename": extract_filename_from_key(key),
            "size": metadata["size"],
        }
    except Exception as e:
        if hasattr(e, "status_code"):
            raise
        handle_s3_error("generate download URL", e, key)

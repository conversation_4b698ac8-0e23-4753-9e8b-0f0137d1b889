import React, { useMemo, useState, useEffect, useRef, useCallback } from "react";
import Plot from "react-plotly.js";
import * as Plotly from "plotly.js";
import { Card } from "@/components/ui/card";
import { PlotControlPanel } from "./PlotControlPanel";
import { ChannelSelectionPanel } from "./ChannelSelectionPanel";

interface HFOEvent {
  channel: string;
  start_time: number;
  end_time: number;
  peak_frequency: number;
  amplitude: number;
}

interface HfoPlotlyViewerProps {
  channelData: Record<string, number[]>;
  hfoEvents: HFOEvent[];
  samplingRate: number;
  duration: number;
  channelLabels: string[];
  metadata?: {
    filename?: string;
    montage?: string;
    frequency_band?: string;
  };
}

// Helper function for intelligent data decimation
const decimateData = (data: number[], maxPoints: number): number[] => {
  if (data.length <= maxPoints) return data;

  const step = Math.ceil(data.length / maxPoints);
  const decimated: number[] = [];

  for (let i = 0; i < data.length; i += step) {
    // Use max value in window to preserve peaks
    const windowEnd = Math.min(i + step, data.length);
    let maxVal = data[i];
    for (let j = i + 1; j < windowEnd; j++) {
      if (Math.abs(data[j]) > Math.abs(maxVal)) {
        maxVal = data[j];
      }
    }
    decimated.push(maxVal);
  }

  return decimated;
};

export const HfoPlotlyViewer: React.FC<HfoPlotlyViewerProps> = React.memo(
  ({ channelData, hfoEvents, samplingRate, duration, channelLabels, metadata }) => {
    // View controls state
    const [timeWindow, setTimeWindow] = useState<[number, number]>([0, Math.min(10, duration)]);
    const [gain, setGain] = useState(20);
    const [showHFOMarkers, setShowHFOMarkers] = useState(true);
    const [showThresholds, setShowThresholds] = useState(false);
    const [selectedChannels, setSelectedChannels] = useState<string[]>(channelLabels);
    const [isPanelCollapsed, setIsPanelCollapsed] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);

    // Calculate HFO count per channel
    const hfoCountPerChannel = useMemo(() => {
      const counts: Record<string, number> = {};
      channelLabels.forEach((ch) => {
        counts[ch] = hfoEvents.filter((e) => e.channel === ch).length;
      });
      return counts;
    }, [hfoEvents, channelLabels]);

    // Calculate channel offset for display - dynamic based on channel count
    const baseOffset = Math.max(100, 800 / Math.max(selectedChannels.length, 1));
    const channelOffset = baseOffset / (gain / 20);

    // Generate plot data with intelligent decimation
    const plotData = useMemo(() => {
      const traces: Plotly.Data[] = [];

      // Calculate optimal decimation level based on window size
      const windowDuration = timeWindow[1] - timeWindow[0];

      // Determine max points to render for performance
      let maxPointsPerChannel: number;
      if (windowDuration <= 10) {
        maxPointsPerChannel = 50000; // Full resolution for < 10 seconds
      } else if (windowDuration <= 60) {
        maxPointsPerChannel = 20000; // Medium resolution for < 1 minute
      } else if (windowDuration <= 300) {
        maxPointsPerChannel = 10000; // Lower resolution for < 5 minutes
      } else {
        maxPointsPerChannel = 5000; // Minimum resolution for full recording
      }

      selectedChannels.forEach((channel, index) => {
        const data = channelData[channel];
        if (!data || data.length === 0) return;

        // Create time axis
        const timeAxis = Array.from({ length: data.length }, (_, i) => i / samplingRate);

        // Filter data within time window
        const startIdx = Math.floor(timeWindow[0] * samplingRate);
        const endIdx = Math.ceil(timeWindow[1] * samplingRate);
        let windowedTime = timeAxis.slice(startIdx, endIdx);
        let windowedData = data.slice(startIdx, endIdx);

        // Apply intelligent decimation if needed
        if (windowedData.length > maxPointsPerChannel) {
          const decimationFactor = Math.ceil(windowedData.length / maxPointsPerChannel);
          windowedData = decimateData(windowedData, maxPointsPerChannel);
          // Also decimate time axis
          const decimatedTime: number[] = [];
          for (let i = 0; i < windowedTime.length; i += decimationFactor) {
            decimatedTime.push(windowedTime[i]);
          }
          windowedTime = decimatedTime;
        }

        // Apply gain and offset
        const scaledData = windowedData.map((val) => (val * 20) / gain);
        const offsetData = scaledData.map((val) => val + index * channelOffset);

        // Main EEG trace with WebGL for performance
        traces.push({
          x: windowedTime,
          y: offsetData,
          type: "scattergl", // Use WebGL for high performance
          mode: "lines",
          name: channel,
          line: {
            color: "#1e293b",
            width: 1,
            simplify: false, // Disable simplification for accuracy
          },
          hovertemplate: `${channel}<br>Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
        });

        // Add HFO markers
        if (showHFOMarkers) {
          const channelHFOs = hfoEvents.filter(
            (hfo) => hfo.channel === channel && hfo.start_time >= timeWindow[0] && hfo.start_time <= timeWindow[1]
          );

          if (channelHFOs.length > 0) {
            // HFO start markers
            traces.push({
              x: channelHFOs.map((hfo) => hfo.start_time),
              y: channelHFOs.map(() => index * channelOffset),
              type: "scattergl", // WebGL for markers too
              mode: "markers",
              name: `${channel} HFOs`,
              marker: {
                color: "#ef4444",
                size: 8,
                symbol: "diamond",
              },
              showlegend: false,
              hovertemplate: "HFO Event<br>Time: %{x:.2f}s<br>Peak Freq: %{customdata:.1f}Hz<extra></extra>",
              customdata: channelHFOs.map((hfo) => hfo.peak_frequency),
            });

            // HFO duration bars with improved visualization
            channelHFOs.forEach((hfo) => {
              const startIdx = Math.floor(hfo.start_time * samplingRate);
              const endIdx = Math.floor(hfo.end_time * samplingRate);
              const hfoTime = timeAxis.slice(startIdx, endIdx);

              if (hfoTime.length > 0) {
                // Duration bar below signal
                traces.push({
                  x: [hfo.start_time, hfo.end_time],
                  y: [index * channelOffset - 10, index * channelOffset - 10],
                  type: "scattergl", // WebGL for all traces
                  mode: "lines",
                  line: {
                    color: "rgba(239, 68, 68, 0.6)",
                    width: 4,
                  },
                  showlegend: false,
                  hovertemplate: `HFO Duration<br>Start: ${hfo.start_time.toFixed(3)}s<br>End: ${hfo.end_time.toFixed(3)}s<br>Duration: ${(
                    (hfo.end_time - hfo.start_time) *
                    1000
                  ).toFixed(1)}ms<extra></extra>`,
                });

                // Vertical lines at start and end of HFO
                traces.push({
                  x: [hfo.start_time, hfo.start_time],
                  y: [index * channelOffset - 20, index * channelOffset + 20],
                  type: "scattergl",
                  mode: "lines",
                  line: {
                    color: "rgba(239, 68, 68, 0.3)",
                    width: 1,
                    dash: "dot",
                  },
                  showlegend: false,
                  hoverinfo: "skip",
                });

                traces.push({
                  x: [hfo.end_time, hfo.end_time],
                  y: [index * channelOffset - 20, index * channelOffset + 20],
                  type: "scattergl",
                  mode: "lines",
                  line: {
                    color: "rgba(239, 68, 68, 0.3)",
                    width: 1,
                    dash: "dot",
                  },
                  showlegend: false,
                  hoverinfo: "skip",
                });
              }
            });
          }
        }

        // Add threshold lines based on actual detection parameters
        if (showThresholds) {
          // Upper threshold line
          const upperThreshold = index * channelOffset + 30;
          traces.push({
            x: windowedTime,
            y: new Array(windowedTime.length).fill(upperThreshold),
            type: "scattergl",
            mode: "lines",
            name: `${channel} upper threshold`,
            line: {
              color: "rgba(59, 130, 246, 0.3)",
              width: 1,
              dash: "dot",
            },
            showlegend: false,
            hovertemplate: "Upper threshold<extra></extra>",
          });

          // Lower threshold line
          const lowerThreshold = index * channelOffset - 30;
          traces.push({
            x: windowedTime,
            y: new Array(windowedTime.length).fill(lowerThreshold),
            type: "scattergl",
            mode: "lines",
            name: `${channel} lower threshold`,
            line: {
              color: "rgba(59, 130, 246, 0.3)",
              width: 1,
              dash: "dot",
            },
            showlegend: false,
            hovertemplate: "Lower threshold<extra></extra>",
          });
        }
      });

      return traces;
    }, [channelData, selectedChannels, hfoEvents, timeWindow, samplingRate, showHFOMarkers, showThresholds, gain, channelOffset]);

    // Layout configuration
    const layout = {
      title: {
        text: metadata?.filename || "HFO Analysis",
        font: {
          family: "system-ui, -apple-system, sans-serif",
          size: 16,
          color: "#1e293b",
        },
      },
      xaxis: {
        title: {
          text: "Time (seconds)",
          font: {
            family: "system-ui, -apple-system, sans-serif",
            size: 12,
            color: "#64748b",
          },
        },
        range: timeWindow,
        gridcolor: "#e2e8f0",
        gridwidth: 0.5,
        linecolor: "#cbd5e1",
        linewidth: 1,
        tickfont: {
          family: "system-ui, -apple-system, sans-serif",
          size: 10,
          color: "#64748b",
        },
        zeroline: false,
      },
      yaxis: {
        title: {
          text: "Channels",
          font: {
            family: "system-ui, -apple-system, sans-serif",
            size: 12,
            color: "#64748b",
          },
        },
        tickmode: "array" as const,
        tickvals: selectedChannels.map((_, i) => i * channelOffset),
        ticktext: selectedChannels,
        range: [selectedChannels.length * channelOffset + channelOffset / 2, -channelOffset / 2],
        autorange: false,
        gridcolor: "#f8fafc",
        gridwidth: 0.5,
        linecolor: "#cbd5e1",
        linewidth: 1,
        tickfont: {
          family: "system-ui, -apple-system, sans-serif",
          size: 10,
          color: "#64748b",
        },
        zeroline: false,
      },
      height: Math.max(600, selectedChannels.length * 80 + 150),
      margin: { l: 100, r: 30, t: 50, b: 60 },
      hovermode: "closest" as const,
      hoverlabel: {
        bgcolor: "white",
        font: {
          family: "system-ui, -apple-system, sans-serif",
          size: 11,
          color: "#1e293b",
        },
        bordercolor: "#e2e8f0",
      },
      showlegend: false,
      plot_bgcolor: "#ffffff",
      paper_bgcolor: "#ffffff",
      annotations: metadata
        ? [
            {
              text: `Montage: ${metadata.montage || "N/A"} | Frequency: ${metadata.frequency_band || "N/A"}`,
              showarrow: false,
              x: 0,
              y: 1.05,
              xref: "paper" as const,
              yref: "paper" as const,
              xanchor: "left" as const,
              font: {
                family: "system-ui, -apple-system, sans-serif",
                size: 10,
                color: "#64748b",
              },
            },
          ]
        : [],
    };

    // Config for plotly with performance optimizations
    const config = {
      displayModeBar: true,
      displaylogo: false,
      modeBarButtonsToRemove: ["lasso2d" as const, "select2d" as const, "autoScale2d" as const],
      modeBarButtonsToAdd: [],
      toImageButtonOptions: {
        format: "png" as const,
        filename: `hfo_analysis_${metadata?.filename?.replace(".edf", "") || "export"}_${new Date().toISOString().split("T")[0]}`,
        height: 1200,
        width: 1920,
        scale: 2,
      },
      responsive: true,
      scrollZoom: true, // Enable scroll zoom for better navigation
      doubleClick: "reset" as const,
      showTips: false, // Disable tips for better performance
      plotGlPixelRatio: 1, // Reduce pixel ratio for better performance
    };

    // Control handlers
    const handleZoomIn = useCallback(() => {
      const center = (timeWindow[0] + timeWindow[1]) / 2;
      const newRange = (timeWindow[1] - timeWindow[0]) * 0.75;
      setTimeWindow([Math.max(0, center - newRange / 2), Math.min(duration, center + newRange / 2)]);
    }, [timeWindow, duration]);

    const handleZoomOut = useCallback(() => {
      const center = (timeWindow[0] + timeWindow[1]) / 2;
      const newRange = Math.min((timeWindow[1] - timeWindow[0]) * 1.33, duration);
      setTimeWindow([Math.max(0, center - newRange / 2), Math.min(duration, center + newRange / 2)]);
    }, [timeWindow, duration]);

    const handleReset = () => {
      setTimeWindow([0, Math.min(10, duration)]);
      setGain(20);
      setShowHFOMarkers(true);
      setShowThresholds(false);
      setSelectedChannels(channelLabels);
    };

    // Navigation handlers
    const handleNavigate = useCallback(
      (direction: "prev" | "next") => {
        const windowSize = timeWindow[1] - timeWindow[0];
        if (direction === "prev") {
          const newStart = Math.max(0, timeWindow[0] - windowSize / 2);
          setTimeWindow([newStart, Math.min(newStart + windowSize, duration)]);
        } else {
          const newStart = Math.min(duration - windowSize, timeWindow[0] + windowSize / 2);
          setTimeWindow([newStart, Math.min(newStart + windowSize, duration)]);
        }
      },
      [timeWindow, duration]
    );

    // Channel selection handlers
    const handleChannelToggle = (channel: string) => {
      setSelectedChannels((prev) => (prev.includes(channel) ? prev.filter((ch) => ch !== channel) : [...prev, channel]));
    };

    const handleSelectAll = () => setSelectedChannels(channelLabels);
    const handleClearAll = () => setSelectedChannels([]);

    // Fullscreen handler
    const handleFullscreen = useCallback(() => {
      if (!document.fullscreenElement && containerRef.current) {
        containerRef.current.requestFullscreen();
        setIsFullscreen(true);
      } else {
        document.exitFullscreen();
        setIsFullscreen(false);
      }
    }, []);

    // Export handler
    const handleExport = () => {
      const plotElement = document.querySelector(".js-plotly-plot") as HTMLElement & { _fullLayout?: unknown; _fullData?: unknown };
      if (plotElement) {
        Plotly.downloadImage(plotElement, {
          format: "png",
          width: 1920,
          height: 1200,
          filename: `hfo_analysis_${metadata?.filename?.replace(".edf", "") || "export"}_${new Date().toISOString().split("T")[0]}`,
        });
      }
    };

    // Keyboard shortcuts
    useEffect(() => {
      const handleKeyPress = (e: KeyboardEvent) => {
        if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return;

        switch (e.key.toLowerCase()) {
          case "a":
            handleZoomIn();
            break;
          case "d":
            handleZoomOut();
            break;
          case "o":
            handleNavigate("prev");
            break;
          case "p":
            handleNavigate("next");
            break;
          case "f":
            handleFullscreen();
            break;
        }
      };

      document.addEventListener("keydown", handleKeyPress);
      return () => document.removeEventListener("keydown", handleKeyPress);
    }, [handleZoomIn, handleZoomOut, handleNavigate, handleFullscreen]);

    // Update fullscreen state on change
    useEffect(() => {
      const handleFullscreenChange = () => {
        setIsFullscreen(!!document.fullscreenElement);
      };

      document.addEventListener("fullscreenchange", handleFullscreenChange);
      return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
    }, []);

    return (
      <div className="space-y-4" ref={containerRef}>
        {/* Enhanced Control Panel */}
        <PlotControlPanel
          timeWindow={timeWindow}
          onTimeWindowChange={setTimeWindow}
          duration={duration}
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onReset={handleReset}
          gain={gain}
          onGainChange={setGain}
          showHFOMarkers={showHFOMarkers}
          onToggleHFOMarkers={() => setShowHFOMarkers(!showHFOMarkers)}
          showThresholds={showThresholds}
          onToggleThresholds={() => setShowThresholds(!showThresholds)}
          onNavigate={handleNavigate}
          onExport={handleExport}
          onFullscreen={handleFullscreen}
          isFullscreen={isFullscreen}
          showKeyboardHints={true}
        />

        {/* Main plot with channel selection */}
        <div className="flex gap-4 h-[80vh] overflow-hidden">
          {/* Channel Selection Panel */}
          <ChannelSelectionPanel
            channels={channelLabels}
            selectedChannels={selectedChannels}
            onChannelToggle={handleChannelToggle}
            onSelectAll={handleSelectAll}
            onClearAll={handleClearAll}
            hfoCountPerChannel={hfoCountPerChannel}
            isCollapsed={isPanelCollapsed}
            onToggleCollapse={() => setIsPanelCollapsed(!isPanelCollapsed)}
          />

          {/* Main plot */}
          <Card className="flex-1 p-4 overflow-auto">
            <div style={{ minHeight: `${Math.max(600, selectedChannels.length * 80 + 150)}px` }}>
              <Plot data={plotData} layout={layout} config={config} useResizeHandler style={{ width: "100%", height: "100%" }} />
            </div>
          </Card>
        </div>

        {/* HFO statistics */}
        <Card className="p-4">
          <div className="flex justify-between text-sm">
            <div>
              <span className="text-gray-600">Total HFOs in view: </span>
              <span className="font-semibold">
                {
                  hfoEvents.filter((h) => h.start_time >= timeWindow[0] && h.start_time <= timeWindow[1] && selectedChannels.includes(h.channel))
                    .length
                }
              </span>
            </div>
            <div>
              <span className="text-gray-600">Channels displayed: </span>
              <span className="font-semibold">{selectedChannels.length}</span>
            </div>
            <div>
              <span className="text-gray-600">Sampling rate: </span>
              <span className="font-semibold">{samplingRate} Hz</span>
            </div>
          </div>
        </Card>
      </div>
    );
  }
);
